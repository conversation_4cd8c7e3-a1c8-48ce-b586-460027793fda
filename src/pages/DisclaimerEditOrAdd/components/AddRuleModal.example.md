# AddRuleModal 使用示例

## 场景1: 快速创建基础规则

### 用户操作流程
1. 点击 "Add Another Rule" 按钮
2. 选择 Category: "Electronics > Smartphones"
3. 点击 "Create Rule Only" 按钮

### 结果
```typescript
// 创建的规则
{
  catId: 123,
  attrConditionInfo: {
    attrConditionList: [] // 空的attribute rules
  }
}
```

### 适用场景
- 快速建立规则框架
- 稍后再添加具体的attribute条件
- 简单的基于category的规则

---

## 场景2: 创建完整的复杂规则

### 用户操作流程
1. 点击 "Add Another Rule" 按钮
2. 选择 Category: "Electronics > Smartphones"
3. 点击 "+ Add Attribute Rule"
4. 添加第一个attribute rule:
   - Attribute: "Brand"
   - Operator: "equals"
   - Value: "Apple"
5. 点击 "+ Add Attribute Rule"
6. 添加第二个attribute rule:
   - Attribute: "Storage"
   - Operator: "greater than"
   - Value: "128GB"
7. 点击 "Create Rule with 2 Attributes" 按钮

### 结果
```typescript
// 创建的规则
{
  catId: 123,
  attrConditionInfo: {
    attrConditionList: [
      {
        attrId: 1,
        operationType: 1, // equals
        value: "Apple",
        valueId: 5
      },
      {
        attrId: 2,
        operationType: 3, // greater than
        value: "128GB",
        valueId: 0 // custom value
      }
    ]
  }
}
```

### 适用场景
- 一次性创建完整规则
- 明确知道所有需要的条件
- 减少后续编辑步骤

---

## 场景3: 预览后选择创建方式

### 用户操作流程
1. 点击 "Add Another Rule" 按钮
2. 选择 Category: "Clothing > T-Shirts"
3. 点击 "+ Add Attribute Rule"
4. 添加 attribute rule:
   - Attribute: "Material"
   - Operator: "equals"
   - Value: "Cotton"
5. 预览已添加的规则
6. 决定是否需要这个attribute rule
7. 选择合适的创建方式:
   - 如果需要: 点击 "Create Rule with 1 Attribute"
   - 如果不需要: 点击 "Create Rule Only"

### 两种结果对比

#### 选择 "Create Rule Only"
```typescript
{
  catId: 456,
  attrConditionInfo: {
    attrConditionList: [] // 忽略已添加的attribute
  }
}
```

#### 选择 "Create Rule with 1 Attribute"
```typescript
{
  catId: 456,
  attrConditionInfo: {
    attrConditionList: [
      {
        attrId: 3,
        operationType: 1,
        value: "Cotton",
        valueId: 8
      }
    ]
  }
}
```

### 适用场景
- 不确定是否需要某些attribute条件
- 想要预览规则效果
- 灵活的决策过程

---

## 界面元素说明

### 步骤指示
- **Step 1: Select Category** - 必选步骤
- **Step 2: Add Attribute Rules (Optional)** - 可选步骤

### 按钮状态
- **未选择category**: 两个创建按钮都禁用
- **已选择category**: 两个创建按钮都启用
- **已添加attributes**: 主按钮文本变为 "Create Rule with X Attribute(s)"

### 视觉反馈
- 选中的category显示在Step 1下方
- 已添加的attribute rules显示在灰色框中
- 每个attribute rule都可以单独删除
- 按钮文本动态反映当前状态

---

## 最佳实践

### 🎯 **简单规则优先**
如果不确定需要哪些attribute条件，建议先使用 "Create Rule Only"，后续在规则卡片中逐步添加。

### 🎯 **批量操作**
如果需要创建多个相似的规则，可以在弹窗中配置好一个完整的规则作为模板。

### 🎯 **预览验证**
利用弹窗中的预览功能验证attribute rules的正确性，避免创建后再修改。

### 🎯 **灵活选择**
根据具体需求选择合适的创建方式，不必拘泥于某一种模式。
