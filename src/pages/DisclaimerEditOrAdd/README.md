# Disclaimer Edit/Add Page - Attribute Rule Modal Implementation

## 功能概述

本次修改将 "Add Attribute Rule" 功能改为弹窗形式，并使用表格展示现有的 Attribute Rules。

## 主要变更

### 1. 新增组件

#### AttributeRuleModal.tsx
- 用于添加/编辑 Attribute Rule 的弹窗组件
- 支持属性选择、操作符选择、值输入
- 自动加载属性值选项（下拉选择或手动输入）
- 表单验证确保所有必填字段完整

#### AttributeRuleTable.tsx
- 用于展示 Attribute Rules 的表格组件
- 支持添加、编辑、删除操作
- 每个操作都通过弹窗进行
- 防止重复选择相同属性

#### BasicCategoryCascader.tsx
- 专门用于 Basic Settings 的分类选择器
- 允许浏览所有层级的分类
- 只有选择叶子节点时才更新值
- 支持多选模式

### 2. 修改的组件

#### CategoryCascader (Category/index.tsx)
- 修改为只允许选择叶子节点
- 父节点可以点击浏览但不会被选中
- 用于 Advanced Settings 中的单个规则分类选择

#### DisclaimerEditOrAdd/index.tsx
- 集成新的表格和弹窗组件
- 移除内联的 Attribute Rule 编辑界面
- 添加 Basic Settings 部分用于快速分类选择
- 优化数据流和状态管理

## 功能特性

### 分类选择行为
1. **Basic Settings**:
   - 只能选择叶子节点（父节点可点击浏览但不会被选中）
   - 支持多选
   - 已在 Advanced Rules 中使用的分类会被禁用

2. **Advanced Settings**:
   - 只能选择叶子节点（父节点可点击浏览但不会被选中）
   - 单选模式
   - 其他规则已使用的分类会被禁用
   - 编辑现有规则时，当前规则的分类仍可重新选择

### Attribute Rule 管理
1. **表格展示**: 清晰显示所有已配置的属性规则
2. **弹窗编辑**: 统一的编辑界面，支持添加和修改
3. **防重复**: 不允许在同一规则中选择重复的属性
4. **智能验证**: 根据属性类型自动调整可用操作符

### 防重复选择
1. **跨规则防重复**: 不同规则之间不能选择相同的分类
2. **实时更新**: 添加/删除规则时，其他选择器的禁用状态实时更新
3. **编辑友好**: 编辑现有规则时，当前规则的分类仍可重新选择

### 用户体验优化
1. **直观操作**: 表格 + 弹窗的组合提供更好的操作体验
2. **清晰反馈**: 明确的错误提示和验证信息
3. **防重复提示**: 已使用的分类显示为禁用状态，提供清晰的视觉反馈
4. **响应式设计**: 适配不同屏幕尺寸

## 使用说明

### 添加新的 Attribute Rule
1. 在 Advanced Settings 中选择一个规则
2. 点击 "Add Attribute Rule" 按钮
3. 在弹窗中选择属性、操作符和值
4. 点击 "Save" 保存

### 编辑现有的 Attribute Rule
1. 在表格中点击编辑按钮
2. 在弹窗中修改相关信息
3. 点击 "Save" 保存更改

### 删除 Attribute Rule
1. 在表格中点击删除按钮
2. 确认删除操作

## 技术实现

### 数据流
- 主组件管理所有状态
- 子组件通过回调函数通知状态变更
- 统一的数据验证和错误处理

### 组件通信
- Props 传递配置和回调函数
- 事件驱动的状态更新
- 清晰的组件职责分离

### 性能优化
- useMemo 缓存计算结果
- 按需加载属性值数据
- 避免不必要的重渲染
