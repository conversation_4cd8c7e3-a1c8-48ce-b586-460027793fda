# AttributeRuleModal - Enhanced Value Selection

## Overview

The `AttributeRuleModal` component has been enhanced to provide a unified dropdown interface that supports both selecting from predefined attribute values and inputting custom values. This improvement provides a better user experience by combining the functionality of dropdown selection and free text input into a single, intuitive interface.

## Features

### 🎯 **Unified Interface**
- Single dropdown component that handles both predefined and custom values
- No more switching between Select and Input components
- Consistent user experience across all attribute types

### 🔍 **Smart Search & Selection**
- **Search functionality**: Type to filter existing options or enter custom values
- **Keyboard support**: Press Enter to select custom values
- **Visual feedback**: Clear indication of custom vs predefined values

### 💡 **Enhanced UX**
- **Dynamic placeholders**: Context-aware placeholder text
- **Custom value hints**: Visual prompt to use custom values when typing
- **Hover effects**: Interactive feedback for better usability
- **Loading states**: Proper loading indicators during API calls

## How It Works

### For Attributes with Predefined Values
1. **Dropdown shows existing options** from the API
2. **Type to search** through existing values
3. **Type custom values** that don't exist in the list
4. **Visual prompt** appears: "⏎ Use custom value: 'your-input'"
5. **Click or press Enter** to select the custom value

### For Attributes without Predefined Values
1. **Direct input mode** with "Enter value" placeholder
2. **Search-enabled dropdown** for consistency
3. **Same custom value selection** workflow

## Technical Implementation

### State Management
```typescript
const [searchValue, setSearchValue] = useState(''); // Tracks user input
const [attrValues, setAttrValues] = useState<globalAttribute.IAttrValue[]>([]); // Predefined values
const [formData, setFormData] = useState<disclaimer.IAttrCondition>({
  attrId: undefined,
  operationType: undefined,
  value: '', // The actual value (string)
  valueId: 0, // ID for predefined values, 0 for custom
});
```

### Value Handling Logic
- **Predefined values**: `valueId > 0`, `value` contains the display text
- **Custom values**: `valueId = 0`, `value` contains the user input
- **Backward compatibility**: Existing data structure unchanged

### Key Functions
```typescript
// Handles both predefined (number) and custom (string) value selection
const handleValueChange = (value: string | number) => { ... }

// Tracks search input for custom value detection
const handleValueSearch = (value: string) => { ... }

// Selects custom value and updates form state
const handleCustomValueSelect = (value: string) => { ... }
```

## User Interactions

### Selecting Predefined Values
1. Click dropdown → See list of existing values
2. Click any value → Selected immediately
3. Type to filter → Narrow down options
4. Click filtered result → Selected

### Entering Custom Values
1. Click dropdown → Start typing
2. Type custom text → "Use custom value" prompt appears
3. Click prompt OR press Enter → Custom value selected
4. Value shows with "Custom:" prefix in future selections

### Keyboard Shortcuts
- **Enter**: Select custom value when typing
- **Escape**: Close dropdown
- **Arrow keys**: Navigate through options
- **Type**: Filter existing options or create custom

## Visual Indicators

### Predefined Values
- Listed normally in dropdown
- No special prefix or styling

### Custom Values
- **In dropdown**: "Custom: [value]" prefix
- **In prompt**: "⏎ Use custom value: '[value]'"
- **Hover effects**: Background color changes
- **Enter icon**: ⏎ symbol for keyboard hint

## API Integration

### Loading Predefined Values
```typescript
const loadAttributeValues = async (attrId: number) => {
  // Checks attribute input type
  // Loads values via getAttrValueListWithPaging
  // Handles pagination automatically
  // Sets loading states appropriately
}
```

### Supported Attribute Types
- `SINGLE_DROP_DOWN`: Shows predefined values + custom input
- `MULTI_DROP_DOWN`: Shows predefined values + custom input  
- `SINGLE_COMBO_BOX`: Shows predefined values + custom input
- `MULTI_COMBO_BOX`: Shows predefined values + custom input
- `FREE_TEXT_FILED`: Direct custom input (no predefined values)

## Benefits

### For Users
- **Faster workflow**: No need to switch between different input types
- **Flexible input**: Can use existing values or create new ones
- **Clear feedback**: Always know what type of value you're selecting
- **Keyboard friendly**: Efficient keyboard-only operation

### For Developers
- **Simplified logic**: Single component handles all scenarios
- **Maintainable code**: Consistent patterns and clear separation of concerns
- **Backward compatible**: No breaking changes to existing data
- **Extensible**: Easy to add new features or modify behavior

## Testing

The component includes comprehensive tests covering:
- Predefined value selection
- Custom value input and selection
- Keyboard interactions (Enter key)
- Different attribute types
- Loading states and error handling
- Editing existing rules with custom values

## Migration Notes

This enhancement is **fully backward compatible**:
- Existing data structure unchanged
- All existing functionality preserved
- No API changes required
- Existing tests continue to work

The improvement is purely additive, enhancing the user experience without breaking existing workflows.
