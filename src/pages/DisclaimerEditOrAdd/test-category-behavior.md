# Category Selection Behavior Test

## 测试场景

### 分类层级结构示例
```
Electronics (父节点)
├── Mobile Phones (父节点)
│   ├── iPhone (叶子节点) ✅ 可选中
│   └── Android (叶子节点) ✅ 可选中
├── Laptops (父节点)
│   ├── Gaming Laptops (叶子节点) ✅ 可选中
│   └── Business Laptops (叶子节点) ✅ 可选中
└── Accessories (叶子节点) ✅ 可选中
```

## 预期行为

### Basic Settings (BasicCategoryCascader)
1. **父节点点击**: 
   - ✅ 可以点击 "Electronics"
   - ✅ 可以点击 "Mobile Phones" 
   - ✅ 可以点击 "Laptops"
   - ❌ 但这些点击不会更新选中值

2. **叶子节点点击**:
   - ✅ 点击 "iPhone" → 值更新为 iPhone 的 ID
   - ✅ 点击 "Android" → 值更新为 Android 的 ID
   - ✅ 点击 "Accessories" → 值更新为 Accessories 的 ID

3. **多选模式**:
   - ✅ 可以同时选择多个叶子节点
   - ❌ 父节点不会被添加到选中值中

### Advanced Settings (CategoryCascader)
1. **父节点点击**:
   - ✅ 可以点击展开子分类
   - ❌ 但不会选中父节点

2. **叶子节点点击**:
   - ✅ 只有叶子节点可以被选中
   - ✅ 选中后值会更新

3. **已使用分类**:
   - ❌ 已在其他规则中使用的分类会被禁用
   - ✅ 当前规则的分类可以重新选择

## 技术实现

### 关键配置
```typescript
// 允许点击任何节点
changeOnSelect: true

// 在 handleChange 中过滤
const handleChange = (selectedValue: any) => {
  if (Array.isArray(selectedValue) && selectedValue.length > 0) {
    const finalValue = selectedValue[selectedValue.length - 1];
    if (isLeafNode(categories, finalValue)) {
      onChange?.(finalValue); // 只有叶子节点才调用 onChange
    }
    // 父节点点击不调用 onChange，保持当前值
  }
};
```

### 叶子节点检测
```typescript
const isLeafNode = (cats: uploadAdmin.ICategory[], catId: number): boolean => {
  for (const cat of cats) {
    if (cat.catId === catId) {
      return !cat.subCategories || cat.subCategories.length === 0;
    }
    if (cat.subCategories) {
      const result = isLeafNode(cat.subCategories, catId);
      if (result !== undefined) return result;
    }
  }
  return false;
};
```

## 用户体验

### 视觉反馈
- 父节点可以点击（不会显示为禁用状态）
- 点击父节点时会展开子分类
- 只有叶子节点的选择会反映在输入框的值中
- 清晰的占位符文本提示用户行为

### 交互流程
1. 用户点击分类选择器
2. 可以点击任何层级的分类进行浏览
3. 点击父节点 → 展开子分类，不更新值
4. 点击叶子节点 → 选中该分类，更新值
5. 选择器关闭，显示选中的叶子节点

这样的实现既满足了"父节点可以选择"的需求（可以点击浏览），又确保了"只有叶子节点才更新值"的业务逻辑。
