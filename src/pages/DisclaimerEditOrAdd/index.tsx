import {
  CloseOutlined,
  DeleteOutlined,
  PlusOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { getCountry } from '@classification/admin-solution';
import {
  Button,
  Col,
  Input,
  message,
  Popconfirm,
  Row,
  Select,
  Space,
  Switch,
  Typography,
} from 'antd';
import React, {
  useEffect,
  useMemo,
  useState,
} from 'react';
import {
  useNavigate,
  useSearchParams,
} from 'react-router-dom';

import type { OperationType } from 'src/api/disclaimer/constants';
import type {
  disclaimer,
} from 'src/api/disclaimer/disclaimer';
import {
  getAttrList,
} from 'src/api/globalAttribute';
import type {
  globalAttribute,
} from 'src/api/globalAttribute/globalAttribute';
import { getGlobalCategoryList } from 'src/api/uploadAdmin';
import type {
  uploadAdmin,
} from 'src/api/uploadAdmin/uploadAdmin';
import ConfigurableCard from 'src/components/ConfigurableCard';
import UploadFile from 'src/components/Upload';
import { DisclaimerOperationMap } from 'src/constants/disclaimer';
import AddRuleModal from './components/AddRuleModal';
import AttributeRuleTable from './components/AttributeRuleTable';
import BasicCategoryCascader from './components/BasicCategoryCascader';
import CategoryCascader from './components/Category';
import style from './style.module.scss';

const { TextArea } = Input;
const { Title, Text } = Typography;



// 可复用的表单字段组件
interface FormFieldProps {
  label: string;
  required?: boolean;
  children: React.ReactNode;
}

const FormField: React.FC<FormFieldProps> = ({ label, required = false, children }) => (
  <div className={ style.formField }>
    <Text strong>
      { label } { required && '*' }
    </Text>
    <div style={ { marginTop: 8 } }>
      { children }
    </div>
  </div>
);

// 可复用的规则卡片组件
interface RuleCardProps {
  rule: disclaimer.IUpdateDisclaimerConditionRequest;
  index: number;
  allRules: disclaimer.IUpdateDisclaimerConditionRequest[]; // 添加所有规则的引用
  availableCategories: uploadAdmin.ICategory[];
  categoryAttributes: Map<number, globalAttribute.IGlobalAttr[]>;
  categoriesLoading: boolean;
  onUpdate: (conditionId: number, index: number, updates: Partial<disclaimer.IUpdateDisclaimerConditionRequest>) => void;
  onDelete: (conditionId: number,index: number) => void;
  onCategoryChange: (categoryId: number) => void;
}

const RuleCard: React.FC<RuleCardProps> = ({
  rule,
  index,
  allRules,
  availableCategories,
  categoryAttributes,
  categoriesLoading,
  onUpdate,
  onDelete,
  onCategoryChange,
}) => {
  // 辅助函数：检查分类是否被使用
  const isCategoryUsed = (category: number): boolean => {
    return allRules.some(otherRule =>
      otherRule.conditionId !== rule.conditionId && otherRule.catId === category,
    );
  };

  // 获取所有已使用的分类ID（排除当前规则）
  const getUsedCategoryIds = (): number[] => {
    return allRules
      .filter(otherRule => otherRule.conditionId !== rule.conditionId)
      .map(otherRule => otherRule.catId)
      .filter((catId): catId is number => catId !== undefined && catId !== null);
  };


  // 处理 Attribute Rule 的增删改
  const handleAddAttributeRule = (attributeRule: disclaimer.IAttrCondition) => {
    const updatedRule = {
      ...rule,
      attrConditionInfo: {
        attrConditionList: [
          ...(rule.attrConditionInfo?.attrConditionList || []),
          attributeRule,
        ],
      },
    };
    onUpdate(rule.conditionId!, index, updatedRule);
  };

  const handleEditAttributeRule = (index: number, attributeRule: disclaimer.IAttrCondition) => {
    const updatedList = [...(rule.attrConditionInfo?.attrConditionList || [])];
    updatedList[index] = attributeRule;
    const updatedRule = {
      ...rule,
      attrConditionInfo: {
        attrConditionList: updatedList,
      },
    };
    onUpdate(rule.conditionId!, index, updatedRule);
  };

  const handleDeleteAttributeRule = (index: number) => {
    const updatedList = [...(rule.attrConditionInfo?.attrConditionList || [])];
    updatedList.splice(index, 1);
    const updatedRule = {
      ...rule,
      attrConditionInfo: {
        attrConditionList: updatedList,
      },
    };
    onUpdate(rule.conditionId!, index, updatedRule);
  };

  return (
    <ConfigurableCard
      key={ rule.conditionId || index }
      header={ {
        title: `Rule ${ index + 1 } — ${ availableCategories.find(cat => cat.catId === rule.catId)?.catName || 'Unnamed' +
        ' Category' }`,
        extra: (
          <Popconfirm
            title="Delete this rule?"
            onConfirm={ () => onDelete(rule.conditionId!) }
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="text"
              danger
              icon={ <DeleteOutlined/> }
              className={ style.deleteButton }
            />
          </Popconfirm>
        ),
      } }
      className={ style.ruleCard }
    >

      <div className={ style.ruleContent }>
        <FormField label="Associated Category" required>
          <BasicCategoryCascader
            categories={ availableCategories }
            value={ rule.catId }
            onChange={ (value) => {
              onUpdate(rule.conditionId!, index, { catId: value });
              // 当分类变化时，加载该分类的属性
              if (value) {
                onCategoryChange(value);
              }
            } }
            placeholder="Select category (leaf nodes only)"
            multiple={ false }
            loading={ categoriesLoading }
            disabled={ categoriesLoading }
            disabledValues={ getUsedCategoryIds() }
          />
        </FormField>

        <AttributeRuleTable
          attributeRules={ rule.attrConditionInfo?.attrConditionList || [] }
          categoryAttributes={ categoryAttributes.get(rule.catId!) || [] }
          onAdd={ handleAddAttributeRule }
          onEdit={ handleEditAttributeRule }
          onDelete={ handleDeleteAttributeRule }
        />
      </div>
    </ConfigurableCard>
  );
};

const DisclaimerEditOrAdd: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const id = searchParams.get('id');
  const isEdit = !!id;

  // 表单状态
  const [formData, setFormData] = useState<{
    name?: string;
    description?: string;
    status?: boolean;
    categories?: string[];
    condition: disclaimer.IUpdateDisclaimerConditionRequest[];
    legalDocument?: { filename: string; hash: string };
  }>({
    name: '',
    description: '',
    status: true,
    categories: [],
    condition: [],
    legalDocument: undefined,
  });

  // UI状态
  const [advancedSettingsExpanded, setAdvancedSettingsExpanded] = useState(false);
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // 动态数据状态
  const [availableCategories, setAvailableCategories] = useState<uploadAdmin.ICategory[]>([]);
  const [categoriesMap, setCategoriesMap] = useState<Map<number, uploadAdmin.ICategory>>(new Map());
  const [categoryAttributes, setCategoryAttributes] = useState<Map<number, globalAttribute.IGlobalAttr[]>>(new Map());
  const [categoriesLoading, setCategoriesLoading] = useState(false);

  // 初始化数据
  useEffect(() => {
    // 加载可用分类
    loadAvailableCategories();

    if (isEdit) {
      loadDisclaimerData();
    }
  }, [id]);

  // 构建分类 Map
  const buildCategoriesMap = (categories: uploadAdmin.ICategory[]): Map<number, uploadAdmin.ICategory> => {
    const map = new Map<number, uploadAdmin.ICategory>();

    const addToMap = (cats: uploadAdmin.ICategory[]) => {
      for (const cat of cats) {
        if (cat.catId) {
          map.set(cat.catId, cat);
        }
        if (cat.subCategories) {
          addToMap(cat.subCategories);
        }
      }
    };

    addToMap(categories);
    return map;
  };

  // 加载可用分类
  const loadAvailableCategories = async () => {
    setCategoriesLoading(true);
    try {
      const response = await getGlobalCategoryList({
        region: getCountry(),
      });
      if (response?.cats) {
        setAvailableCategories(response.cats);
        // 同时构建 categoriesMap
        const map = buildCategoriesMap(response.cats);
        setCategoriesMap(map);
      }
    } catch (error) {
      message.error('Failed to load categories');
    } finally {
      setCategoriesLoading(false);
    }
  };

  // 根据分类加载属性
  const loadCategoryAttributes = async (id: number) => {
    // 检查是否已经加载过该分类的属性
    if (categoryAttributes.has(id)) {
      return;
    }

    try {
      const response = await getAttrList({
        globalCatId: id,
        region: getCountry(),
      });

      if (response?.data?.length) {
        setCategoryAttributes(prev => new Map(prev).set(id, response.data!));
      } else {
      }
    } catch (error) {
    }
  };

  // 加载免责声明数据
  const loadDisclaimerData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      //
      // // 根据ID加载不同的模拟数据
      // if (id === '1') {
      //   // Alcohol Consumption
      //   setFormData({
      //     id: '1',
      //     name: 'Alcohol Consumption',
      //     description: 'The sale and consumption of alcoholic beverages are prohibited for minors. Drink responsibly.',
      //     status: true,
      //     categories: ['Cerveja e Cidra', 'Vinho e Champanhe', 'Licores e Destilados'],
      //     condition: {},
      //     legalDocument: {
      //       filename: 'alcohol_legal_approval.pdf',
      //       hash: 'alcohol_legal_approval_hash',
      //     },
      //   });
      // } else if (id === '2') {
      //   // Baby Formula Warning
      //   setFormData({
      //     id: '2',
      //     name: 'Baby Formula Warning',
      //     description: 'This product should only be administered under the supervision of a qualified healthcare professional. Breastfeeding is recommended up to 2 (two) years of age or beyond, as it is essential for the child\'s development. The information provided here does not replace personalized medical advice.',
      //     status: true,
      //     categories: [],
      //     advancedRules: [
      //       {
      //         id: 1,
      //         conditionId: 1,
      //         catId: 4, // Milk Formula category
      //         attrConditionInfo: [
      //           {
      //             id: 1,
      //             attrId: 1,
      //             operationType: OperationType.OPERATION_TYPE_EQUAL, // equals
      //             valueId: 4,
      //             value: '4', // ID for 'Growing-Up Milk (3+ years)'
      //             attributeName: 'Baby life Stage',
      //             attributeInputType: AttrInputType.SINGLE_DROP_DOWN,
      //             inputValidatorType: AttrInputValidatorType.VALIDATOR_STRING,
      //             operator: '=',
      //             valueType: 'id',
      //           },
      //         ],
      //       },
      //     ],
      //   });
      // }
    } catch (error) {
      message.error('Failed to load disclaimer data');
    } finally {
      setLoading(false);
    }
  };

  // 处理表单字段变化
  const handleFieldChange = (field: keyof any, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // 获取所有已使用的分类ID（用于 Basic Settings）
  const getAllUsedCategoryIds = (): number[] => {
    return formData.condition
      .map(rule => rule.catId)
      .filter((catId): catId is number => catId !== undefined && catId !== null);
  };

  // 处理分类变化
  const handleCategoryChange = (categories: number[]) => {
    // 使用 categoriesMap 将 category ID 转换为 category 名称
    const categoryNames = categories.map(catId => {
      const category = categoriesMap.get(catId);
      return category?.catName || '';
    }).filter(name => name !== '');

    setFormData(prev => ({
      ...prev,
      categories: categoryNames,
    }));
  };

  // 添加高级规则
  const addAdvancedRule = () => {
    // 找到第一个未被使用的分类
    const usedCategories = formData.condition.map(rule => rule.catId);
    const availableCategory = availableCategories
    .find(cat => !usedCategories.includes(cat.catId)); // 使用 cat.catId

    const newRule: disclaimer.IUpdateDisclaimerConditionRequest = {
      catId: availableCategory?.catId || 0,
      attrConditionInfo: {
        attrConditionList: [],
      },
    };

    setFormData(prev => ({
      ...prev,
      condition: [...prev.condition, newRule],
    }));

    // 如果没有可用分类，显示提示
    if (!availableCategory) {
      message.warning('All categories are already used. Please remove a rule first or use a different category.');
    }
  };

  // 删除高级规则
  const removeAdvancedRule = (conditionId: number) => {
    setFormData(prev => ({
      ...prev,
      condition: prev.condition.filter(rule => rule.conditionId !== conditionId),
    }));
  };

  // 更新高级规则
  const updateAdvancedRule = (conditionId: number, updates: Partial<disclaimer.IUpdateDisclaimerConditionRequest>) => {
    setFormData(prev => ({
      ...prev,
      condition: prev.condition.map(rule => {
        if (rule.conditionId === conditionId) {
          // 如果更新的是分类，清空所有属性规则
          if (updates.catId && updates.catId !== rule.catId) {
            return {
              ...rule,
              ...updates,
              attrConditionInfo: {
                attrConditionList: [], // 清空属性规则
              },
            };
          }
          return { ...rule, ...updates };
        }
        return rule;
      }),
    }));
  };



  // 处理文件上传
  const handleFileUpload = async (data?: { hash?: string; filename?: string }) => {
    if (data) {
      setFormData(prev => ({
        ...prev,
        legalDocument: {
          filename: data.filename || '',
          hash: data.hash || '',
        },
      }));
      message.success('File uploaded successfully');
    } else {
      setFormData(prev => ({
        ...prev,
        legalDocument: undefined,
      }));
      message.success('File removed successfully');
    }
  };

  // 校验属性规则是否完整
  const validateAttributeRules = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    formData.condition.forEach((rule, ruleIndex) => {
      if (rule.attrConditionInfo?.attrConditionList && rule.attrConditionInfo.attrConditionList.length > 0) {
        rule.attrConditionInfo.attrConditionList.forEach((attrRule, attrIndex) => {
          // 检查属性是否选择
          if (!attrRule.attrId) {
            errors.push(`Rule ${ ruleIndex + 1 }: Attribute ${ attrIndex + 1 } - Please select an attribute`);
          }

          // 检查操作符是否选择
          if (!attrRule.operationType) {
            errors.push(`Rule ${ ruleIndex + 1 }: Attribute ${ attrIndex + 1 } - Please select an operator`);
          }

          // 检查值是否填写
          if (!attrRule.value || !attrRule.value.trim()) {
            errors.push(`Rule ${ ruleIndex + 1 }: Attribute ${ attrIndex + 1 } - Please enter a value`);
          }
        });
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  };

  // 保存免责声明
  const handleSave = async () => {
    // 基础字段校验
    if (!formData.name.trim()) {
      message.error('Please enter disclaimer name');
      return;
    }

    if (!formData.description.trim()) {
      message.error('Please enter disclaimer description');
      return;
    }

    // 属性规则校验
    const validation = validateAttributeRules();
    if (!validation.isValid) {
      // 存储错误信息到状态中
      setValidationErrors(validation.errors);
      // 展开高级设置区域以显示错误
      setAdvancedSettingsExpanded(true);
      // 显示第一个错误信息
      message.error(`Validation failed: ${ validation.errors.length } error(s) found. Please check the Advanced Settings section.`);
      return;
    }

    // 清除之前的错误信息
    setValidationErrors([]);

    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      message.success(`Disclaimer ${ isEdit ? 'updated' : 'created' } successfully`);
      navigate('/product/disclaimers-management');
    } catch (error) {
      message.error(`Failed to ${ isEdit ? 'update' : 'create' } disclaimer`);
    } finally {
      setLoading(false);
    }
  };

  // 取消编辑
  const handleCancel = () => {
    navigate('/product/disclaimers-management');
  };

  // 生成规则摘要
  const generateRuleSummary = () => {
    const { categories, condition } = formData;

    if (categories && categories.length > 0 && condition.length === 0) {
      return `All items in: ${ categories.join(', ') } categories`;
    } else if (condition.length > 0) {
      const summaries = condition.map(rule => {
        const category = availableCategories.find(cat => cat.catId === rule.catId);
        const name = category?.catName || `Category ${rule.catId}`;
        if (!rule.attrConditionInfo?.attrConditionList || rule.attrConditionInfo.attrConditionList.length === 0) {
          return `Items in "${ name }" category`;
        } else {
          const attributeSummary = rule.attrConditionInfo.attrConditionList.map(ar => {
            const attr = categoryAttributes.get(rule.catId!)?.find(a => a.id === ar.attrId);
            const operatorName = DisclaimerOperationMap[ar.operationType as OperationType] || ar.operationType;
            return `${ attr?.name || ar.attrId } ${ operatorName } ${ ar.value }`;
          }).join(', ');
          return `Items in "${ name }" category where ${ attributeSummary }`;
        }
      });
      return summaries.join('; ');
    }

    return 'No rules configured';
  };

  return (
    <div className={ style.container }>
      {/* Header */ }
      <div className={ style.header }>
        <Title level={ 2 }>
          { isEdit ? 'Edit' : 'Add' } Disclaimer — { formData.name || 'New Disclaimer' }
        </Title>
        <Button
          type="text"
          icon={ <CloseOutlined/> }
          onClick={ handleCancel }
          className={ style.closeButton }
        />
      </div>

      {/* General Information */ }
      <ConfigurableCard
        header={ { title: 'General Information' } }
        className={ style.sectionCard }
      >
        <FormField label="Disclaimer Name" required>
          <Input
            placeholder="Enter disclaimer name"
            value={ formData.name }
            onChange={ (e) => handleFieldChange('name', e.target.value) }
          />
        </FormField>

        <FormField label="Disclaimer Description" required>
          <TextArea
            placeholder="Enter disclaimer description"
            value={ formData.description }
            onChange={ (e) => handleFieldChange('description', e.target.value) }
            rows={ 4 }
          />
        </FormField>

        <FormField label="Status">
          <div className={ style.statusToggle }>
            <Switch
              checked={ formData.status }
              onChange={ (checked) => handleFieldChange('status', checked) }
            />
            <Text className={ style.statusText }>
              { formData.status ? 'Active' : 'Inactive' }
            </Text>
          </div>
        </FormField>
      </ConfigurableCard>

      {/* Advanced Settings */ }
      <ConfigurableCard
        header={
          {
            title: 'Advanced Settings',
            icon: <SettingOutlined className={ style.sectionIcon }/>,
            description: <>Configure specific rules based on category + attribute conditions</>,
          }
        }
        collapsible={ true }
        defaultCollapsed={ !advancedSettingsExpanded }
        className={ style.sectionCard }
        onCollapse={ (collapsed) => setAdvancedSettingsExpanded(!collapsed) }
      >
        <div className={ style.advancedContent }>
          {/* 校验错误提示 */ }
          { validationErrors.length > 0 && (
            <div className={ style.validationErrors }>
              <Text type="danger" strong>Validation Errors:</Text>
              <ul className={ style.errorList }>
                { validationErrors.map((error, index) => (
                  <li key={ index } className={ style.errorItem }>
                    <Text type="danger">{ error }</Text>
                  </li>
                )) }
              </ul>
            </div>
          ) }

          { formData.condition.length === 0 ? (
            <div className={ style.noRules }>
              <Text type="secondary">No advanced rules configured</Text>
            </div>
          ) : (
            formData.condition.map((rule, index) => (
              <RuleCard
                key={ rule.conditionId }
                rule={ rule }
                index={ index }
                allRules={ formData.condition }
                availableCategories={ availableCategories }
                categoryAttributes={ categoryAttributes }
                categoriesLoading={ categoriesLoading }
                onUpdate={ updateAdvancedRule }
                onDelete={ removeAdvancedRule }
                onCategoryChange={ loadCategoryAttributes }
              />
            ))
          ) }

          <Button
            type="primary"
            icon={ <PlusOutlined/> }
            onClick={ addAdvancedRule }
            style={ { marginTop: 16 } }
          >
            Add Another Rule
          </Button>
        </div>
      </ConfigurableCard>

      {/* Rule Summary */ }
      <ConfigurableCard
        header={ {
          title: <div style={ { textAlign: 'center' } }>
            Rule Summary
          </div>,
        } }
        className={ style.sectionCard }
      >
        <div className={ style.ruleSummary }>
          <Text className={ style.ruleSummaryTitle }>This disclaimer will be applied to:</Text>
          <div className={ style.ruleSummaryContent }>
            • { generateRuleSummary() }
          </div>
        </div>
      </ConfigurableCard>

      {/* Legal Approval Document */ }
      <ConfigurableCard
        header={ { title: 'Legal Approval Document' } }
        className={ style.sectionCard }
      >
        <FormField label="Approval Document">
          <UploadFile
            file={ formData.legalDocument || { hash: '', filename: '' } }
            onChange={ handleFileUpload }
            mode="edit"
          />
        </FormField>

        <div className={ style.uploadNote }>
          <Text type="secondary">This file is only for internal reference.</Text>
        </div>
      </ConfigurableCard>

      {/* Footer Actions */ }
      <div className={ style.footer }>
        <Space>
          <Button onClick={ handleCancel }>
            Cancel
          </Button>
          <Button
            type="primary"
            loading={ loading }
            onClick={ handleSave }
          >
            Save Disclaimer
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default DisclaimerEditOrAdd;

