import {
  Button,
  message,
  Modal,
  Typography,
} from 'antd';
import React, {
  useEffect,
  useState,
} from 'react';

import type {
  disclaimer,
} from 'src/api/disclaimer/disclaimer';
import type {
  globalAttribute,
} from 'src/api/globalAttribute/globalAttribute';
import type {
  uploadAdmin,
} from 'src/api/uploadAdmin/uploadAdmin';
import AttributeRuleTable from './AttributeRuleTable';
import BasicCategoryCascader from './BasicCategoryCascader';

const { Text } = Typography;

interface EditCategoryModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (updates: { catId: number; attrConditionInfo: disclaimer.IAttrConditions }) => void;
  currentRule: disclaimer.IUpdateDisclaimerConditionRequest;
  availableCategories: uploadAdmin.ICategory[];
  categoryAttributes: Map<number, globalAttribute.IGlobalAttr[]>;
  categoriesLoading: boolean;
  usedCategoryIds: number[];
  onCategoryChange: (categoryId: number) => void;
}

const EditCategoryModal: React.FC<EditCategoryModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  currentRule,
  availableCategories,
  categoryAttributes,
  categoriesLoading,
  usedCategoryIds,
  onCategoryChange,
}) => {
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>();
  const [attributeRules, setAttributeRules] = useState<disclaimer.IAttrCondition[]>([]);

  // 初始化状态
  useEffect(() => {
    if (visible && currentRule) {
      setSelectedCategoryId(currentRule.catId);
      setAttributeRules(currentRule.attrConditionInfo?.attrConditionList || []);
    }
  }, [visible, currentRule]);

  // 处理category选择变化
  const handleCategoryChange = (categoryId: number) => {
    if (categoryId !== selectedCategoryId) {
      // 切换category时清空attribute rules
      setAttributeRules([]);
      onCategoryChange(categoryId);
    }
    setSelectedCategoryId(categoryId);
  };

  // 处理attribute rule添加
  const handleAddAttributeRule = (attributeRule: disclaimer.IAttrCondition) => {
    setAttributeRules(prev => [...prev, attributeRule]);
  };

  // 处理attribute rule编辑
  const handleEditAttributeRule = (index: number, attributeRule: disclaimer.IAttrCondition) => {
    setAttributeRules(prev => {
      const newRules = [...prev];
      newRules[index] = attributeRule;
      return newRules;
    });
  };

  // 处理attribute rule删除
  const handleDeleteAttributeRule = (index: number) => {
    setAttributeRules(prev => prev.filter((_, i) => i !== index));
  };

  // 处理确认
  const handleConfirm = () => {
    if (!selectedCategoryId) {
      message.warning('Please select a category');
      return;
    }

    onConfirm({
      catId: selectedCategoryId,
      attrConditionInfo: {
        attrConditionList: attributeRules,
      },
    });
  };

  // 获取选中category的名称
  const getSelectedCategoryName = () => {
    if (!selectedCategoryId) return '';
    const category = availableCategories.find(cat => cat.catId === selectedCategoryId);
    return category?.catName || '';
  };

  // 获取可用的categories（排除已使用的，但包含当前的）
  const getAvailableCategories = () => {
    return availableCategories.filter(cat =>
      !usedCategoryIds.includes(cat.catId!) || cat.catId === currentRule.catId
    );
  };

  return (
    <Modal
      title="Edit Category and Attribute Rules"
      open={visible}
      onCancel={onCancel}
      onOk={handleConfirm}
      okText="Save Changes"
      cancelText="Cancel"
      okButtonProps={{ disabled: !selectedCategoryId }}
      width={800}
    >
      <div style={{ padding: '16px 0' }}>
        {/* Category Selection */}
        <div style={{ marginBottom: 24 }}>
          <Text strong style={{ display: 'block', marginBottom: 8 }}>
            Associated Category
          </Text>
          <BasicCategoryCascader
            categories={getAvailableCategories()}
            value={selectedCategoryId}
            onChange={handleCategoryChange}
            placeholder="Select category (leaf nodes only)"
            multiple={false}
            loading={categoriesLoading}
            disabled={categoriesLoading}
            style={{ width: '100%' }}
          />
          {selectedCategoryId && (
            <Text type="secondary" style={{ fontSize: 12, marginTop: 8, display: 'block' }}>
              Selected: <Text strong>{getSelectedCategoryName()}</Text>
              {selectedCategoryId !== currentRule.catId && (
                <Text type="warning" style={{ marginLeft: 8 }}>
                  (Category changed - attribute rules have been cleared)
                </Text>
              )}
            </Text>
          )}
        </div>

        {/* Attribute Rules Table */}
        {selectedCategoryId && (
          <div>
            <Text strong style={{ display: 'block', marginBottom: 8 }}>
              Attribute Rules
            </Text>
            <AttributeRuleTable
              attributeRules={attributeRules}
              categoryAttributes={categoryAttributes.get(selectedCategoryId) || []}
              onAdd={handleAddAttributeRule}
              onEdit={handleEditAttributeRule}
              onDelete={handleDeleteAttributeRule}
            />
          </div>
        )}
      </div>
    </Modal>
  );
};

export default EditCategoryModal;
