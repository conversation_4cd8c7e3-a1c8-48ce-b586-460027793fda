import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import AttributeRuleModal from '../AttributeRuleModal';
import { AttrInputType } from 'src/api/uploadAdmin/constants';
import type { globalAttribute, disclaimer } from 'src/api/globalAttribute/globalAttribute';

// Mock the API call
jest.mock('src/api/globalAttribute', () => ({
  getAttrValueListWithPaging: jest.fn(),
}));

const mockCategoryAttributes: globalAttribute.IGlobalAttr[] = [
  {
    id: 1,
    name: 'Color',
    inputType: AttrInputType.SINGLE_DROP_DOWN,
  },
  {
    id: 2,
    name: 'Size',
    inputType: AttrInputType.FREE_TEXT_FILED,
  },
];

const mockAttrValues: globalAttribute.IAttrValue[] = [
  { id: 1, value: 'Red', globalAttrId: 1 },
  { id: 2, value: 'Blue', globalAttrId: 1 },
  { id: 3, value: 'Green', globalAttrId: 1 },
];

const defaultProps = {
  visible: true,
  onCancel: jest.fn(),
  onOk: jest.fn(),
  categoryAttributes: mockCategoryAttributes,
  usedAttributeIds: [],
};

describe('AttributeRuleModal - Enhanced Value Selection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    const { getAttrValueListWithPaging } = require('src/api/globalAttribute');
    getAttrValueListWithPaging.mockResolvedValue({
      data: mockAttrValues,
      nextOffset: null,
    });
  });

  it('should show dropdown with predefined values when attribute has values', async () => {
    render(<AttributeRuleModal {...defaultProps} />);
    
    // Select an attribute that has predefined values
    const attributeSelect = screen.getByPlaceholderText('Select attribute');
    await userEvent.click(attributeSelect);
    await userEvent.click(screen.getByText('Color'));

    // Wait for values to load
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Select or enter value')).toBeInTheDocument();
    });

    // Click on value dropdown
    const valueSelect = screen.getByPlaceholderText('Select or enter value');
    await userEvent.click(valueSelect);

    // Should show predefined values
    expect(screen.getByText('Red')).toBeInTheDocument();
    expect(screen.getByText('Blue')).toBeInTheDocument();
    expect(screen.getByText('Green')).toBeInTheDocument();
  });

  it('should allow custom value input when typing', async () => {
    render(<AttributeRuleModal {...defaultProps} />);
    
    // Select an attribute
    const attributeSelect = screen.getByPlaceholderText('Select attribute');
    await userEvent.click(attributeSelect);
    await userEvent.click(screen.getByText('Color'));

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Select or enter value')).toBeInTheDocument();
    });

    // Type a custom value
    const valueSelect = screen.getByPlaceholderText('Select or enter value');
    await userEvent.click(valueSelect);
    await userEvent.type(valueSelect, 'Purple');

    // Should show custom value option
    await waitFor(() => {
      expect(screen.getByText('Use custom value: "Purple"')).toBeInTheDocument();
    });
  });

  it('should handle Enter key for custom value selection', async () => {
    render(<AttributeRuleModal {...defaultProps} />);
    
    // Select an attribute
    const attributeSelect = screen.getByPlaceholderText('Select attribute');
    await userEvent.click(attributeSelect);
    await userEvent.click(screen.getByText('Color'));

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Select or enter value')).toBeInTheDocument();
    });

    // Type a custom value and press Enter
    const valueSelect = screen.getByPlaceholderText('Select or enter value');
    await userEvent.click(valueSelect);
    await userEvent.type(valueSelect, 'Purple');
    await userEvent.keyboard('{Enter}');

    // Should select the custom value
    await waitFor(() => {
      expect(valueSelect).toHaveDisplayValue('Purple');
    });
  });

  it('should show input placeholder for attributes without predefined values', async () => {
    render(<AttributeRuleModal {...defaultProps} />);
    
    // Select an attribute without predefined values (FREE_TEXT_FILED)
    const attributeSelect = screen.getByPlaceholderText('Select attribute');
    await userEvent.click(attributeSelect);
    await userEvent.click(screen.getByText('Size'));

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Enter value')).toBeInTheDocument();
    });
  });

  it('should display custom values with "Custom:" prefix in options', async () => {
    const editingRule: disclaimer.IAttrCondition = {
      attrId: 1,
      operationType: 1,
      value: 'Purple',
      valueId: 0, // Custom value
    };

    render(<AttributeRuleModal {...defaultProps} editingRule={editingRule} />);

    await waitFor(() => {
      const valueSelect = screen.getByDisplayValue('Purple');
      expect(valueSelect).toBeInTheDocument();
    });

    // Click to open dropdown
    const valueSelect = screen.getByDisplayValue('Purple');
    await userEvent.click(valueSelect);

    // Should show the custom value with prefix
    await waitFor(() => {
      expect(screen.getByText('Custom: Purple')).toBeInTheDocument();
    });
  });
});
