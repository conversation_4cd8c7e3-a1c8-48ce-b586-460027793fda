import {
  Modal,
  Typography,
  message,
} from 'antd';
import React, {
  useEffect,
  useState,
} from 'react';

import type {
  disclaimer,
} from 'src/api/disclaimer/disclaimer';
import type {
  globalAttribute,
} from 'src/api/globalAttribute/globalAttribute';
import type {
  uploadAdmin,
} from 'src/api/uploadAdmin/uploadAdmin';
import BasicCategoryCascader from './BasicCategoryCascader';

const { Text } = Typography;

interface AddRuleModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (rule: disclaimer.IUpdateDisclaimerConditionRequest) => void;
  availableCategories: uploadAdmin.ICategory[];
  categoryAttributes: Map<number, globalAttribute.IGlobalAttr[]>;
  categoriesLoading: boolean;
  usedCategoryIds: number[];
  onCategoryChange: (categoryId: number) => void;
}

const AddRuleModal: React.FC<AddRuleModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  availableCategories,
  categoryAttributes,
  categoriesLoading,
  usedCategoryIds,
  onCategoryChange,
}) => {
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>();

  // 重置状态
  useEffect(() => {
    if (visible) {
      setSelectedCategoryId(undefined);
    }
  }, [visible]);

  // 处理category选择
  const handleCategoryChange = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
    onCategoryChange(categoryId);
  };

  // 处理确认 - 创建只有category的规则
  const handleConfirm = () => {
    if (!selectedCategoryId) {
      message.warning('Please select a category first');
      return;
    }

    const newRule: disclaimer.IUpdateDisclaimerConditionRequest = {
      catId: selectedCategoryId,
      attrConditionInfo: {
        attrConditionList: [],
      },
    };

    onConfirm(newRule);
  };

  // 获取选中category的名称
  const getSelectedCategoryName = () => {
    if (!selectedCategoryId) return '';
    const category = availableCategories.find(cat => cat.catId === selectedCategoryId);
    return category?.catName || '';
  };

  // 获取可用的categories（排除已使用的）
  const getAvailableCategories = () => {
    return availableCategories.filter(cat => !usedCategoryIds.includes(cat.catId!));
  };

  return (
    <Modal
      title="Add New Rule"
      open={visible}
      onCancel={onCancel}
      onOk={handleConfirm}
      okText="Add Rule"
      cancelText="Cancel"
      okButtonProps={{ disabled: !selectedCategoryId }}
      width={500}
    >
      <div style={{ padding: '16px 0' }}>
        <Text strong style={{ display: 'block', marginBottom: 8 }}>
          Select Category *
        </Text>
        <BasicCategoryCascader
          categories={getAvailableCategories()}
          value={selectedCategoryId}
          onChange={handleCategoryChange}
          placeholder="Select category (leaf nodes only)"
          multiple={false}
          loading={categoriesLoading}
          disabled={categoriesLoading}
          style={{ width: '100%' }}
        />
        {getAvailableCategories().length === 0 && (
          <Text type="secondary" style={{ fontSize: 12, marginTop: 8, display: 'block' }}>
            All categories are already used. Please remove a rule first.
          </Text>
        )}
        {selectedCategoryId && (
          <Text type="secondary" style={{ fontSize: 12, marginTop: 8, display: 'block' }}>
            Selected: <Text strong>{getSelectedCategoryName()}</Text>
            <br />
            You can add attribute rules after creating the rule.
          </Text>
        )}
      </div>
    </Modal>
  );
};

export default AddRuleModal;
