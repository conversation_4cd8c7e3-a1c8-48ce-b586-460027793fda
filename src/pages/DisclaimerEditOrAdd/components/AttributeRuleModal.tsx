import {
  Col,
  Input,
  Modal,
  Row,
  Select,
  Typography,
} from 'antd';
import React, {
  useEffect,
  useState,
} from 'react';

import { OperationType } from 'src/api/disclaimer/constants';
import type {
  disclaimer,
} from 'src/api/disclaimer/disclaimer';
import {
  getAttrValueListWithPaging,
} from 'src/api/globalAttribute';
import type {
  globalAttribute,
} from 'src/api/globalAttribute/globalAttribute';
import {
  AttrInputType,
  AttrInputValidatorType,
} from 'src/api/uploadAdmin/constants';
import { DisclaimerOperationMap } from 'src/constants/disclaimer';

const { Text } = Typography;

interface AttributeRuleModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (attributeRule: disclaimer.IAttrCondition) => void;
  categoryAttributes: globalAttribute.IGlobalAttr[];
  usedAttributeIds: number[];
  editingRule?: disclaimer.IAttrCondition;
  title?: string;
}

const AttributeRuleModal: React.FC<AttributeRuleModalProps> = ({
  visible,
  onCancel,
  onOk,
  categoryAttributes,
  usedAttributeIds,
  editingRule,
  title = 'Add Attribute Rule',
}) => {
  const [formData, setFormData] = useState<disclaimer.IAttrCondition>({
    attrId: undefined,
    operationType: undefined,
    value: '',
    valueId: 0,
  });
  const [attrValues, setAttrValues] = useState<globalAttribute.IAttrValue[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  // 重置表单数据
  useEffect(() => {
    if (visible) {
      if (editingRule) {
        setFormData({ ...editingRule });
        if (editingRule.attrId) {
          loadAttributeValues(editingRule.attrId);
        }
      } else {
        setFormData({
          attrId: undefined,
          operationType: undefined,
          value: '',
          valueId: 0,
        });
        setAttrValues([]);
      }
      setSearchValue('');
    }
  }, [visible, editingRule]);

  // 加载属性值选项
  const loadAttributeValues = async (attrId: number) => {
    const selectedAttr = categoryAttributes.find(attr => attr.id === attrId);
    if (selectedAttr?.inputType === AttrInputType.SINGLE_DROP_DOWN ||
        selectedAttr?.inputType === AttrInputType.MULTI_DROP_DOWN ||
        selectedAttr?.inputType === AttrInputType.SINGLE_COMBO_BOX ||
        selectedAttr?.inputType === AttrInputType.MULTI_COMBO_BOX
    ) {
      setLoading(true);
      try {
        const list: globalAttribute.IAttrValue[] = [];
        const getAttrValues = async (offset: number) => {
          const res = await getAttrValueListWithPaging({
            globalAttrId: attrId,
            limit: 100,
            offset,
          });
          list.push(...(res?.data || []));
          if (res?.nextOffset) {
            await getAttrValues(res.nextOffset);
          }
        };
        await getAttrValues(0);
        setAttrValues(list);
      } catch (error) {
        console.error('Failed to load attribute values:', error);
        setAttrValues([]);
      } finally {
        setLoading(false);
      }
    } else {
      setAttrValues([]);
    }
  };

  // 获取可用的操作符
  const getOperators = (attrId: number): number[] => {
    const selectedAttr = categoryAttributes.find(attr => attr.id === attrId);
    if (!selectedAttr) return [];

    const { inputType, inputValidator } = selectedAttr;

    const baseOperators = {
      [AttrInputType.SINGLE_DROP_DOWN]: [OperationType.OPERATION_TYPE_EQUAL, OperationType.OPERATION_TYPE_NOT_EQUAL],
      [AttrInputType.SINGLE_COMBO_BOX]: [
        OperationType.OPERATION_TYPE_EQUAL, OperationType.OPERATION_TYPE_NOT_EQUAL,
        OperationType.OPERATION_TYPE_GREATER, OperationType.OPERATION_TYPE_LESS,
        OperationType.OPERATION_TYPE_GREATER_EQUAL, OperationType.OPERATION_TYPE_LESS_EQUAL,
      ],
      [AttrInputType.FREE_TEXT_FILED]: [
        OperationType.OPERATION_TYPE_EQUAL, OperationType.OPERATION_TYPE_NOT_EQUAL,
        OperationType.OPERATION_TYPE_GREATER, OperationType.OPERATION_TYPE_LESS,
        OperationType.OPERATION_TYPE_GREATER_EQUAL, OperationType.OPERATION_TYPE_LESS_EQUAL,
      ],
      [AttrInputType.MULTI_DROP_DOWN]: [OperationType.OPERATION_TYPE_EQUAL, OperationType.OPERATION_TYPE_NOT_EQUAL],
      [AttrInputType.MULTI_COMBO_BOX]: [
        OperationType.OPERATION_TYPE_EQUAL, OperationType.OPERATION_TYPE_NOT_EQUAL,
        OperationType.OPERATION_TYPE_GREATER, OperationType.OPERATION_TYPE_LESS,
        OperationType.OPERATION_TYPE_GREATER_EQUAL, OperationType.OPERATION_TYPE_LESS_EQUAL,
      ],
    };

    const validatorRestrictions: Record<AttrInputValidatorType, { exclude: OperationType[] }> = {
      [AttrInputValidatorType.VALIDATOR_STRING]: {
        exclude: [
          OperationType.OPERATION_TYPE_GREATER,
          OperationType.OPERATION_TYPE_LESS,
          OperationType.OPERATION_TYPE_GREATER_EQUAL,
          OperationType.OPERATION_TYPE_LESS_EQUAL,
        ],
      },
      [AttrInputValidatorType.VALIDATOR_INTEGERS]: { exclude: [] },
      [AttrInputValidatorType.VALIDATOR_DATE]: { exclude: [] },
      [AttrInputValidatorType.VALIDATOR_NUMBERS]: { exclude: [] },
      [AttrInputValidatorType.VALIDATOR_NOT_REQUIRED]: { exclude: [] },
    };

    const operators = baseOperators[inputType as AttrInputType] || [];
    const restrictions = validatorRestrictions[inputValidator as AttrInputValidatorType];

    if (restrictions?.exclude) {
      return operators.filter(op => !restrictions.exclude.includes(op));
    }

    return operators;
  };

  // 处理属性选择变化
  const handleAttributeChange = (attrId: number) => {
    setFormData(prev => ({
      ...prev,
      attrId,
      operationType: undefined,
      value: '',
      valueId: 0,
    }));
    setSearchValue('');
    loadAttributeValues(attrId);
  };

  // 处理操作符变化
  const handleOperatorChange = (operationType: number) => {
    setFormData(prev => ({
      ...prev,
      operationType,
    }));
  };

  // 处理值变化
  const handleValueChange = (value: string | number) => {
    if (typeof value === 'number') {
      // 从下拉选择的值
      const selectedValue = attrValues.find(val => val.id === value);
      setFormData(prev => ({
        ...prev,
        value: selectedValue?.value || '',
        valueId: value,
      }));
    } else {
      // 手动输入的值
      setFormData(prev => ({
        ...prev,
        value,
        valueId: 0,
      }));
    }
    setSearchValue('');
  };

  // 处理搜索输入
  const handleValueSearch = (value: string) => {
    setSearchValue(value);
  };

  // 处理自定义值选择
  const handleCustomValueSelect = (value: string) => {
    setFormData(prev => ({
      ...prev,
      value,
      valueId: 0,
    }));
    setSearchValue('');
  };

  // 验证表单
  const validateForm = (): boolean => {
    return !!(formData.attrId && formData.operationType && formData.value?.trim());
  };

  // 处理确认
  const handleOk = () => {
    if (validateForm()) {
      onOk(formData);
    }
  };

  // 获取可用的属性选项
  const availableAttributes = categoryAttributes.filter(attr =>
    !usedAttributeIds.includes(attr.id!) || attr.id === editingRule?.attrId
  );

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      onOk={handleOk}
      okText="Save"
      cancelText="Cancel"
      okButtonProps={{ disabled: !validateForm() }}
      width={600}
    >
      <div style={{ padding: '16px 0' }}>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={24}>
            <Text strong>Associated Attribute *</Text>
            <Select
              placeholder="Select attribute"
              value={formData.attrId}
              onChange={handleAttributeChange}
              style={{ width: '100%', marginTop: 4 }}
              options={availableAttributes.map(attr => ({
                label: attr.name,
                value: attr.id,
              }))}
            />
          </Col>
        </Row>

        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Text strong>Operator *</Text>
            <Select
              placeholder="Select operator"
              value={formData.operationType}
              onChange={handleOperatorChange}
              style={{ width: '100%', marginTop: 4 }}
              disabled={!formData.attrId}
              options={formData.attrId ? getOperators(formData.attrId).map(op => ({
                label: DisclaimerOperationMap[op as OperationType],
                value: op,
              })) : []}
            />
          </Col>
          <Col span={12}>
            <Text strong>Value *</Text>
            <Select
              placeholder={attrValues.length > 0 ? "Select or enter value" : "Enter value"}
              value={formData.valueId && formData.valueId > 0 ? formData.valueId : formData.value}
              onChange={handleValueChange}
              onSearch={handleValueSearch}
              onInputKeyDown={(e) => {
                if (e.key === 'Enter' && searchValue && !attrValues.some(val => val.value === searchValue)) {
                  e.preventDefault();
                  handleCustomValueSelect(searchValue);
                }
              }}
              style={{ width: '100%', marginTop: 4 }}
              loading={loading}
              showSearch
              allowClear
              filterOption={false}
              notFoundContent={loading ? 'Loading...' : 'No matching options'}
              dropdownRender={(menu) => (
                <div>
                  {menu}
                  {searchValue && !attrValues.some(val => val.value === searchValue) && (
                    <div
                      style={{
                        padding: '8px 12px',
                        borderTop: '1px solid #f0f0f0',
                        cursor: 'pointer',
                        color: '#1890ff',
                        backgroundColor: '#f9f9f9'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#e6f7ff';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = '#f9f9f9';
                      }}
                      onClick={() => handleCustomValueSelect(searchValue)}
                    >
                      <span style={{ marginRight: 8 }}>⏎</span>
                      Use custom value: "{searchValue}"
                    </div>
                  )}
                </div>
              )}
              options={[
                ...attrValues.map(val => ({
                  label: val.value,
                  value: val.id,
                })),
                ...(formData.value && formData.valueId === 0 && !attrValues.some(val => val.value === formData.value) ? [{
                  label: `Custom: ${formData.value}`,
                  value: formData.value,
                }] : [])
              ]}
            />
          </Col>
        </Row>
      </div>
    </Modal>
  );
};

export default AttributeRuleModal;
