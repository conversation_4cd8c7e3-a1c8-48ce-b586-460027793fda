# 防重复选择功能示例

## 场景描述

假设我们有以下分类结构：
```
Electronics
├── Mobile Phones
│   ├── iPhone (叶子节点)
│   └── Android (叶子节点)
├── Laptops
│   ├── Gaming Laptops (叶子节点)
│   └── Business Laptops (叶子节点)
└── Accessories (叶子节点)
```

## 防重复选择逻辑

### 1. Basic Settings 中的防重复
当用户在 Basic Settings 中选择分类时，已经在 Advanced Rules 中使用的分类会被禁用。

**示例场景：**
- Rule 1 选择了 "iPhone"
- Rule 2 选择了 "Gaming Laptops"

**结果：**
- Basic Settings 中 "iPhone" 和 "Gaming Laptops" 会显示为禁用状态
- 用户无法在 Basic Settings 中再次选择这些分类

### 2. Advanced Settings 中的防重复
在 Advanced Settings 中，每个 Rule 都不能选择已被其他 Rule 使用的分类。

**示例场景：**
- Rule 1 已选择 "iPhone"
- 用户创建 Rule 2

**结果：**
- Rule 2 的分类选择器中，"iPhone" 会显示为禁用状态
- 用户只能选择其他未被使用的叶子节点

### 3. 编辑现有 Rule 时的行为
当编辑现有 Rule 时，该 Rule 当前选择的分类不会被视为"已使用"。

**示例场景：**
- Rule 1 选择了 "iPhone"
- 用户编辑 Rule 1

**结果：**
- Rule 1 的分类选择器中，"iPhone" 仍然可选（因为是当前 Rule 的选择）
- 其他 Rule 使用的分类仍然被禁用

## 技术实现

### 获取已使用的分类ID
```typescript
// 在 RuleCard 组件中 - 排除当前规则
const getUsedCategoryIds = (): number[] => {
  return allRules
    .filter(otherRule => otherRule.conditionId !== rule.conditionId)
    .map(otherRule => otherRule.catId)
    .filter((catId): catId is number => catId !== undefined && catId !== null);
};

// 在主组件中 - 获取所有已使用的分类
const getAllUsedCategoryIds = (): number[] => {
  return formData.condition
    .map(rule => rule.catId)
    .filter((catId): catId is number => catId !== undefined && catId !== null);
};
```

### 分类选择器配置
```typescript
// Advanced Settings 中的 CategoryCascader
<CategoryCascader
  categories={availableCategories}
  value={rule.catId}
  onChange={handleCategoryChange}
  disabledValues={getUsedCategoryIds()} // 排除当前规则
  placeholder="Select category (leaf nodes only)"
/>

// Basic Settings 中的 BasicCategoryCascader
<BasicCategoryCascader
  categories={availableCategories}
  value={formData.categories}
  onChange={handleCategoryChange}
  disabledValues={getAllUsedCategoryIds()} // 包含所有已使用的分类
  placeholder="Select categories (leaf nodes only, no duplicates)"
  multiple={true}
/>
```

## 用户体验

### 视觉反馈
- ❌ **禁用状态**: 已使用的分类显示为灰色，无法点击
- ✅ **可用状态**: 未使用的叶子节点正常显示，可以选择
- 📝 **提示文本**: 占位符明确说明"no duplicates"

### 错误预防
- 🚫 **前端验证**: 在选择器层面就阻止重复选择
- 🔍 **实时更新**: 当添加新规则时，其他选择器立即更新禁用状态
- 🔄 **动态调整**: 删除规则时，被释放的分类重新变为可选

### 操作流程
1. **创建第一个规则**: 所有叶子节点都可选
2. **选择分类**: 选中的分类在其他地方变为禁用
3. **创建第二个规则**: 只能选择剩余的可用分类
4. **编辑现有规则**: 当前规则的分类仍可重新选择
5. **删除规则**: 释放的分类重新变为可用

## 边界情况处理

### 1. 所有分类都被使用
- 显示友好的提示信息
- 建议用户删除不需要的规则或使用 Basic Settings

### 2. 分类数据加载中
- 显示加载状态
- 禁用选择器直到数据加载完成

### 3. 分类数据为空
- 显示"无可用分类"提示
- 提供重新加载的选项

这样的实现确保了：
- ✅ 不同规则之间不会有重复的分类选择
- ✅ 用户可以清楚地看到哪些分类已被使用
- ✅ 编辑现有规则时体验流畅
- ✅ 系统状态始终保持一致
