import {
  Button,
  Modal,
  Space,
  Typography,
  message,
} from 'antd';
import React, {
  useEffect,
  useState,
} from 'react';

import type {
  disclaimer,
} from 'src/api/disclaimer/disclaimer';
import type {
  globalAttribute,
} from 'src/api/globalAttribute/globalAttribute';
import type {
  uploadAdmin,
} from 'src/api/uploadAdmin/uploadAdmin';
import AttributeRuleModal from './AttributeRuleModal';
import BasicCategoryCascader from './BasicCategoryCascader';

const { Text } = Typography;

interface AddRuleModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (rule: disclaimer.IUpdateDisclaimerConditionRequest) => void;
  availableCategories: uploadAdmin.ICategory[];
  categoryAttributes: Map<number, globalAttribute.IGlobalAttr[]>;
  categoriesLoading: boolean;
  usedCategoryIds: number[];
  onCategoryChange: (categoryId: number) => void;
}

const AddRuleModal: React.FC<AddRuleModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  availableCategories,
  categoryAttributes,
  categoriesLoading,
  usedCategoryIds,
  onCategoryChange,
}) => {
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>();
  const [showAttributeModal, setShowAttributeModal] = useState(false);
  const [attributeRules, setAttributeRules] = useState<disclaimer.IAttrCondition[]>([]);

  // 重置状态
  useEffect(() => {
    if (visible) {
      setSelectedCategoryId(undefined);
      setShowAttributeModal(false);
      setAttributeRules([]);
    }
  }, [visible]);

  // 处理category选择
  const handleCategoryChange = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
    onCategoryChange(categoryId);
  };

  // 处理添加attribute rule
  const handleAddAttributeRule = () => {
    if (!selectedCategoryId) {
      message.warning('Please select a category first');
      return;
    }
    setShowAttributeModal(true);
  };

  // 处理attribute rule确认
  const handleAttributeRuleOk = (attributeRule: disclaimer.IAttrCondition) => {
    setAttributeRules(prev => [...prev, attributeRule]);
    setShowAttributeModal(false);
  };

  // 处理直接确认（不添加attribute rule）
  const handleDirectConfirm = () => {
    if (!selectedCategoryId) {
      message.warning('Please select a category first');
      return;
    }

    const newRule: disclaimer.IUpdateDisclaimerConditionRequest = {
      catId: selectedCategoryId,
      attrConditionInfo: {
        attrConditionList: attributeRules,
      },
    };

    onConfirm(newRule);
  };

  // 处理最终确认（有attribute rules）
  const handleFinalConfirm = () => {
    if (!selectedCategoryId) {
      message.warning('Please select a category first');
      return;
    }

    const newRule: disclaimer.IUpdateDisclaimerConditionRequest = {
      catId: selectedCategoryId,
      attrConditionInfo: {
        attrConditionList: attributeRules,
      },
    };

    onConfirm(newRule);
  };

  // 获取选中category的名称
  const getSelectedCategoryName = () => {
    if (!selectedCategoryId) return '';
    const category = availableCategories.find(cat => cat.catId === selectedCategoryId);
    return category?.catName || '';
  };

  // 获取可用的categories（排除已使用的）
  const getAvailableCategories = () => {
    return availableCategories.filter(cat => !usedCategoryIds.includes(cat.catId!));
  };

  return (
    <>
      <Modal
        title="Add New Rule"
        open={visible}
        onCancel={onCancel}
        width={600}
        footer={null}
      >
        <div style={{ padding: '16px 0' }}>
          {/* Step 1: Select Category */}
          <div style={{ marginBottom: 24 }}>
            <Text strong style={{ display: 'block', marginBottom: 8 }}>
              Step 1: Select Category *
            </Text>
            <BasicCategoryCascader
              categories={getAvailableCategories()}
              value={selectedCategoryId}
              onChange={handleCategoryChange}
              placeholder="Select category (leaf nodes only)"
              multiple={false}
              loading={categoriesLoading}
              disabled={categoriesLoading}
              style={{ width: '100%' }}
            />
            {getAvailableCategories().length === 0 && (
              <Text type="secondary" style={{ fontSize: 12, marginTop: 4, display: 'block' }}>
                All categories are already used. Please remove a rule first.
              </Text>
            )}
          </div>

          {/* Step 2: Add Attribute Rules (Optional) */}
          {selectedCategoryId && (
            <div style={{ marginBottom: 24 }}>
              <Text strong style={{ display: 'block', marginBottom: 8 }}>
                Step 2: Add Attribute Rules (Optional)
              </Text>
              <Text type="secondary" style={{ display: 'block', marginBottom: 12, fontSize: 12 }}>
                Selected category: <Text strong>{getSelectedCategoryName()}</Text>
              </Text>
              
              {attributeRules.length > 0 && (
                <div style={{ marginBottom: 12, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
                  <Text strong style={{ display: 'block', marginBottom: 8 }}>
                    Added Attribute Rules ({attributeRules.length}):
                  </Text>
                  {attributeRules.map((rule, index) => (
                    <div key={index} style={{ marginBottom: 4 }}>
                      <Text style={{ fontSize: 12 }}>
                        • Attribute {rule.attrId} {rule.operationType} "{rule.value}"
                      </Text>
                    </div>
                  ))}
                </div>
              )}

              <Button
                type="dashed"
                onClick={handleAddAttributeRule}
                style={{ width: '100%', marginBottom: 12 }}
              >
                Add Attribute Rule
              </Button>
            </div>
          )}

          {/* Footer Actions */}
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8, paddingTop: 16, borderTop: '1px solid #f0f0f0' }}>
            <Button onClick={onCancel}>
              Cancel
            </Button>
            <Button
              type="primary"
              onClick={attributeRules.length > 0 ? handleFinalConfirm : handleDirectConfirm}
              disabled={!selectedCategoryId}
            >
              {attributeRules.length > 0 ? 'Add Rule with Attributes' : 'Add Rule'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Attribute Rule Modal */}
      <AttributeRuleModal
        visible={showAttributeModal}
        onCancel={() => setShowAttributeModal(false)}
        onOk={handleAttributeRuleOk}
        categoryAttributes={categoryAttributes.get(selectedCategoryId!) || []}
        usedAttributeIds={attributeRules.map(rule => rule.attrId!)}
        title="Add Attribute Rule"
      />
    </>
  );
};

export default AddRuleModal;
