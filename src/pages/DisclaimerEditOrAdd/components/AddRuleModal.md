# AddRuleModal - 添加规则弹窗

## 概述

`AddRuleModal` 是一个新的弹窗组件，用于替代原来的直接添加规则功能。当用户点击"Add Another Rule"按钮时，会打开这个弹窗让用户手动选择category，而不是自动选择第一个可用的category。

## 功能特性

### 🎯 **手动Category选择**
- 用户必须手动选择category，不再自动分配
- 只显示未被使用的categories
- 清晰的视觉反馈显示选中的category

### 🚫 **防重复选择**
- 自动过滤已被使用的categories
- 当所有categories都被使用时显示提示信息
- 确保每个category只能被使用一次

### 💡 **灵活的规则创建**
- 两步式操作：选择category → 可选添加attribute rules → 确认
- 两种创建选项：只创建category规则 或 创建包含attributes的规则
- 实时预览已添加的attribute rules
- 支持添加和删除attribute rules

## 用户交互流程

### 1. 点击"Add Another Rule"
- 打开AddRuleModal弹窗
- 显示category选择器

### 2. 选择Category
- 从可用的categories中选择一个
- 只显示leaf nodes（叶子节点）
- 已使用的categories被自动过滤

### 3. 添加Attribute Rules（可选）
- **添加**: 点击"+ Add Attribute Rule"打开attribute选择弹窗
- **预览**: 查看已添加的attribute rules列表
- **删除**: 移除不需要的attribute rules

### 4. 选择创建方式
- **Create Rule Only**: 只创建category规则，忽略所有attribute rules
- **Create Rule**: 创建包含所有已添加attribute rules的规则
- **Cancel**: 关闭弹窗，不创建规则

### 5. 后续操作
- 规则创建后显示在Advanced Settings中
- 可以在规则卡片中继续编辑attribute rules
- 可以编辑或删除整个规则

## 创建选项详解

### 🎯 **Create Rule Only**
- **用途**: 快速创建只包含category的规则
- **行为**: 忽略所有已添加的attribute rules，创建空的attrConditionList
- **适用场景**:
  - 想要先创建基础规则，稍后再添加attributes
  - 只需要基于category的简单规则
  - 快速建立规则框架

### 🎯 **Create Rule**
- **用途**: 创建包含所有已配置attribute rules的完整规则
- **行为**: 将所有已添加的attribute rules包含在新规则中
- **适用场景**:
  - 一次性创建完整的复杂规则
  - 已经明确知道需要哪些attribute条件
  - 减少后续编辑步骤

### 💡 **使用建议**
- **简单规则**: 使用"Create Rule Only"，后续在规则卡片中添加attributes
- **复杂规则**: 在弹窗中添加所有需要的attributes，然后使用"Create Rule"
- **不确定**: 可以先添加一些attributes预览效果，然后选择合适的创建方式

## 技术实现

### 组件接口
```typescript
interface AddRuleModalProps {
  visible: boolean;                                    // 弹窗显示状态
  onCancel: () => void;                               // 取消回调
  onConfirm: (rule: disclaimer.IUpdateDisclaimerConditionRequest) => void; // 确认回调
  availableCategories: uploadAdmin.ICategory[];       // 所有可用categories
  categoryAttributes: Map<number, globalAttribute.IGlobalAttr[]>; // category属性映射
  categoriesLoading: boolean;                         // 加载状态
  usedCategoryIds: number[];                          // 已使用的category IDs
  onCategoryChange: (categoryId: number) => void;     // category变化回调
}
```

### 状态管理
```typescript
const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>();
const [attributeRules, setAttributeRules] = useState<disclaimer.IAttrCondition[]>([]);
const [showAttributeModal, setShowAttributeModal] = useState(false);
```

### 核心逻辑
```typescript
// 过滤可用categories
const getAvailableCategories = () => {
  return availableCategories.filter(cat => !usedCategoryIds.includes(cat.catId!));
};

// 创建只有category的规则
const handleCreateRuleOnly = () => {
  const newRule: disclaimer.IUpdateDisclaimerConditionRequest = {
    catId: selectedCategoryId,
    attrConditionInfo: {
      attrConditionList: [], // 始终为空
    },
  };
  onConfirm(newRule);
};

// 创建包含attributes的规则
const handleCreateRuleWithAttributes = () => {
  const newRule: disclaimer.IUpdateDisclaimerConditionRequest = {
    catId: selectedCategoryId,
    attrConditionInfo: {
      attrConditionList: attributeRules, // 包含所有已添加的attributes
    },
  };
  onConfirm(newRule);
};
```

## 与原实现的对比

### 原实现 (addAdvancedRule)
```typescript
const addAdvancedRule = () => {
  // 自动选择第一个可用category
  const availableCategory = availableCategories
    .find(cat => !usedCategories.includes(cat.catId));
  
  const newRule = {
    catId: availableCategory?.catId || 0, // 自动分配
    attrConditionInfo: { attrConditionList: [] },
  };
  
  // 直接添加到formData
  setFormData(prev => ({
    ...prev,
    condition: [...prev.condition, newRule],
  }));
};
```

### 新实现 (AddRuleModal)
```typescript
const addAdvancedRule = () => {
  setAddRuleModalVisible(true); // 打开弹窗
};

const handleAddRuleConfirm = (rule) => {
  // 用户手动选择后的回调
  const newRule = { ...rule, conditionId: Date.now() };
  setFormData(prev => ({
    ...prev,
    condition: [...prev.condition, newRule],
  }));
  setAddRuleModalVisible(false);
};
```

## 优势

### 🎯 **更好的用户控制**
- 用户明确知道自己在选择什么category
- 避免意外的自动分配
- 更清晰的操作流程

### 🛡️ **更好的错误预防**
- 防止选择重复的categories
- 清晰的可用选项显示
- 明确的状态反馈

### 🔧 **更好的可维护性**
- 分离的组件职责
- 清晰的数据流
- 易于测试和扩展

## 使用示例

```tsx
<AddRuleModal
  visible={addRuleModalVisible}
  onCancel={() => setAddRuleModalVisible(false)}
  onConfirm={handleAddRuleConfirm}
  availableCategories={availableCategories}
  categoryAttributes={categoryAttributes}
  categoriesLoading={categoriesLoading}
  usedCategoryIds={getUsedCategoryIds()}
  onCategoryChange={loadCategoryAttributes}
/>
```

## 未来扩展

这个组件设计为可扩展的，未来可以添加：
- 批量添加多个规则
- 预设的category模板
- 更复杂的category筛选逻辑
- 规则预览功能
