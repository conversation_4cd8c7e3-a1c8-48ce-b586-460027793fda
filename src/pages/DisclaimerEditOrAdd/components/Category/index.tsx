import type { CascaderProps } from 'antd';
import { Cascader } from 'antd';
import React, { useMemo } from 'react';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import style from 'src/pages/DisclaimerEditOrAdd/components/Category/style.module.scss';

interface CategoryOption {
  label: string;
  value: number;
  children?: CategoryOption[];
}

interface CategoryCascaderProps {
  categories: uploadAdmin.ICategory[];
  value?: number | number[] | string | string[];
  onChange?: (value: any) => void;
  placeholder?: string;
  multiple?: boolean;
  loading?: boolean;
  disabled?: boolean;
  style?: React.CSSProperties;
  disabledValues?: number[];
}

const CategoryCascader: React.FC<CategoryCascaderProps> = ({
  categories,
  value,
  onChange,
  placeholder = 'Select category',
  multiple = false,
  loading = false,
  disabled = false,
  style: customStyle,
  disabledValues = [],
}) => {
  // 检查是否为叶子节点
  const isLeafNode = (cats: uploadAdmin.ICategory[], catId: number): boolean => {
    for (const cat of cats) {
      if (cat.catId === catId) {
        return !cat.subCategories || cat.subCategories.length === 0;
      }
      if (cat.subCategories) {
        const result = isLeafNode(cat.subCategories, catId);
        if (result !== undefined) return result;
      }
    }
    return false;
  };

  // 转换分类数据为Cascader所需的格式
  const categoryOptions = useMemo(() => {
    const convertToOptions = (cats: uploadAdmin.ICategory[]): CategoryOption[] => {
      return cats.map(cat => {
        const hasChildren = cat.subCategories && cat.subCategories.length > 0;
        return {
          label: cat.catName || '',
          value: cat.catId || 0,
          children: hasChildren ? convertToOptions(cat.subCategories!) : undefined,
          // 只禁用在禁用列表中的分类，父节点不禁用（允许点击展开）
          disabled: disabledValues.includes(cat.catId || 0),
        };
      });
    };

    return convertToOptions(categories);
  }, [categories, disabledValues]);

  // 处理值变化 - 只有选择叶子节点时才更新值
  const handleChange = (selectedValue: any) => {
    if (multiple) {
      // 对于多选，过滤出叶子节点
      const leafValues = selectedValue.filter((path: number[]) => {
        const leafCatId = path[path.length - 1];
        return isLeafNode(categories, leafCatId);
      });
      onChange?.(leafValues);
    } else {
      // 对于单选，检查是否为叶子节点
      if (Array.isArray(selectedValue) && selectedValue.length > 0) {
        const finalValue = selectedValue[selectedValue.length - 1];
        if (isLeafNode(categories, finalValue)) {
          onChange?.(finalValue);
        }
        // 如果不是叶子节点，不调用 onChange，保持当前值不变
      }
    }
  };

  // 处理单选时的值格式转换
  const processedValue = useMemo(() => {
    if (multiple) {
      return value;
    }

    // 对于单选，需要找到完整的路径
    if (typeof value === 'number' && value > 0) {
      const findPath = (options: CategoryOption[], targetValue: number): number[] | null => {
        for (const option of options) {
          if (option.value === targetValue) {
            return [option.value];
          }
          if (option.children) {
            const childPath = findPath(option.children, targetValue);
            if (childPath) {
              return [option.value, ...childPath];
            }
          }
        }
        return null;
      };

      return findPath(categoryOptions, value) || undefined;
    }

    return undefined;
  }, [value, categoryOptions, multiple]);

  const cascaderProps: CascaderProps<any> = {
    options: categoryOptions,
    value: processedValue,
    onChange: handleChange,
    placeholder: loading ? 'Loading categories...' : placeholder,
    multiple,
    loading,
    disabled: disabled || loading,
    style: { width: '100%', ...customStyle },
    showSearch: true,
    changeOnSelect: true, // 允许选择任何节点，但在 handleChange 中过滤
    expandTrigger: 'click', // 点击展开和选择
  };

  return <Cascader {...cascaderProps} />;
};

export default CategoryCascader;
