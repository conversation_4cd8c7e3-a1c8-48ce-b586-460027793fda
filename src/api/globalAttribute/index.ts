import { globalAttribute } from './globalAttribute';
import { api<PERSON><PERSON><PERSON>, WithResponse } from 'src/api/helper/apiHandler';
import { request } from 'src/api/helper';

const URLPrefix = '/wsa/api/v2/classification/attribute';

function createRequestFunc<IRequest, IResponse>(url: string) {
  return function (
    requestBody: IRequest,
    config?: { returnFullResponse?: boolean; skipError?: boolean }
  ) {
    return apiHandler(
      request<WithResponse<IResponse>>({
        url: URLPrefix + url,
        data: requestBody,
      }),
      config
    );
  };
}

export const getAttrList = createRequestFunc<
  globalAttribute.IGetAttrListRequest,
  globalAttribute.IGetAttrListResponse
>('/get_attr_list');

export const getAttrValueListWithPaging = createRequestFunc<
  globalAttribute.IGetAttrValueListWithPagingRequest,
  globalAttribute.IGetAttrValueListWithPagingResponse
>('/get_attr_value_list_with_paging');
