# AddRuleModal - 添加规则弹窗

## 概述

`AddRuleModal` 是一个新的弹窗组件，用于替代原来的直接添加规则功能。当用户点击"Add Another Rule"按钮时，会打开这个弹窗让用户手动选择category，而不是自动选择第一个可用的category。

## 功能特性

### 🎯 **手动Category选择**
- 用户必须手动选择category，不再自动分配
- 只显示未被使用的categories
- 清晰的视觉反馈显示选中的category

### 🚫 **防重复选择**
- 自动过滤已被使用的categories
- 当所有categories都被使用时显示提示信息
- 确保每个category只能被使用一次

### 💡 **简洁的用户体验**
- 简单的单步操作：选择category → 确认
- 清晰的状态反馈和提示信息
- 创建规则后可以在规则卡片中添加attribute rules

## 用户交互流程

### 1. 点击"Add Another Rule"
- 打开AddRuleModal弹窗
- 显示category选择器

### 2. 选择Category
- 从可用的categories中选择一个
- 只显示leaf nodes（叶子节点）
- 已使用的categories被自动过滤

### 3. 确认或取消
- **确认**: 创建新规则，包含选中的category
- **取消**: 关闭弹窗，不创建规则

### 4. 后续操作
- 规则创建后显示在Advanced Settings中
- 可以在规则卡片中添加attribute rules
- 可以编辑或删除规则

## 技术实现

### 组件接口
```typescript
interface AddRuleModalProps {
  visible: boolean;                                    // 弹窗显示状态
  onCancel: () => void;                               // 取消回调
  onConfirm: (rule: disclaimer.IUpdateDisclaimerConditionRequest) => void; // 确认回调
  availableCategories: uploadAdmin.ICategory[];       // 所有可用categories
  categoryAttributes: Map<number, globalAttribute.IGlobalAttr[]>; // category属性映射
  categoriesLoading: boolean;                         // 加载状态
  usedCategoryIds: number[];                          // 已使用的category IDs
  onCategoryChange: (categoryId: number) => void;     // category变化回调
}
```

### 状态管理
```typescript
const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>();
```

### 核心逻辑
```typescript
// 过滤可用categories
const getAvailableCategories = () => {
  return availableCategories.filter(cat => !usedCategoryIds.includes(cat.catId!));
};

// 创建新规则
const handleConfirm = () => {
  const newRule: disclaimer.IUpdateDisclaimerConditionRequest = {
    catId: selectedCategoryId,
    attrConditionInfo: {
      attrConditionList: [], // 初始为空，后续可添加
    },
  };
  onConfirm(newRule);
};
```

## 与原实现的对比

### 原实现 (addAdvancedRule)
```typescript
const addAdvancedRule = () => {
  // 自动选择第一个可用category
  const availableCategory = availableCategories
    .find(cat => !usedCategories.includes(cat.catId));
  
  const newRule = {
    catId: availableCategory?.catId || 0, // 自动分配
    attrConditionInfo: { attrConditionList: [] },
  };
  
  // 直接添加到formData
  setFormData(prev => ({
    ...prev,
    condition: [...prev.condition, newRule],
  }));
};
```

### 新实现 (AddRuleModal)
```typescript
const addAdvancedRule = () => {
  setAddRuleModalVisible(true); // 打开弹窗
};

const handleAddRuleConfirm = (rule) => {
  // 用户手动选择后的回调
  const newRule = { ...rule, conditionId: Date.now() };
  setFormData(prev => ({
    ...prev,
    condition: [...prev.condition, newRule],
  }));
  setAddRuleModalVisible(false);
};
```

## 优势

### 🎯 **更好的用户控制**
- 用户明确知道自己在选择什么category
- 避免意外的自动分配
- 更清晰的操作流程

### 🛡️ **更好的错误预防**
- 防止选择重复的categories
- 清晰的可用选项显示
- 明确的状态反馈

### 🔧 **更好的可维护性**
- 分离的组件职责
- 清晰的数据流
- 易于测试和扩展

## 使用示例

```tsx
<AddRuleModal
  visible={addRuleModalVisible}
  onCancel={() => setAddRuleModalVisible(false)}
  onConfirm={handleAddRuleConfirm}
  availableCategories={availableCategories}
  categoryAttributes={categoryAttributes}
  categoriesLoading={categoriesLoading}
  usedCategoryIds={getUsedCategoryIds()}
  onCategoryChange={loadCategoryAttributes}
/>
```

## 未来扩展

这个组件设计为可扩展的，未来可以添加：
- 批量添加多个规则
- 预设的category模板
- 更复杂的category筛选逻辑
- 规则预览功能
