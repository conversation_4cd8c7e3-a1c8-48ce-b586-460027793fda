import type { CascaderProps } from 'antd';
import { Cascader } from 'antd';
import React, { useMemo } from 'react';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';

interface CategoryOption {
  label: string;
  value: number;
  children?: CategoryOption[];
}

interface BasicCategoryCascaderProps {
  categories?: uploadAdmin.ICategory[]; // Keep for backward compatibility
  categoriesMap: Map<number, uploadAdmin.ICategory>;
  value?: number | number[] | string | string[];
  onChange?: (value: any) => void;
  placeholder?: string;
  multiple?: boolean;
  loading?: boolean;
  disabled?: boolean;
  style?: React.CSSProperties;
  disabledValues?: number[];
}

const BasicCategoryCascader = ({
  categories, // Keep for backward compatibility
  categoriesMap,
  value,
  onChange,
  placeholder = 'Select category',
  multiple = false,
  loading = false,
  disabled = false,
  style: customStyle,
  disabledValues = [],
}: BasicCategoryCascaderProps) => {

  // 检查是否为叶子节点 - 使用 Map 结构
  const isLeafNode = (catId: number): boolean => {
    const category = categoriesMap.get(catId);
    if (!category) return false;
    return !category.subCategories || category.subCategories.length === 0;
  };

  // 从 Map 构建根级分类列表
  const buildRootCategories = (): uploadAdmin.ICategory[] => {
    // 如果提供了 categories 数组，使用它（向后兼容）
    if (categories && categories.length > 0) {
      return categories;
    }

    // 否则从 Map 中构建根级分类
    const rootCategories: uploadAdmin.ICategory[] = [];
    const allCategories = Array.from(categoriesMap.values());

    // 找出所有根级分类（没有父分类的分类）
    for (const category of allCategories) {
      const isRoot = !allCategories.some(cat =>
        cat.subCategories?.some(sub => sub.catId === category.catId)
      );
      if (isRoot) {
        rootCategories.push(category);
      }
    }

    return rootCategories;
  };

  // 转换分类数据为Cascader所需的格式
  const categoryOptions = useMemo(() => {
    const convertToOptions = (cats: uploadAdmin.ICategory[]): CategoryOption[] => {
      return cats.map(cat => {
        const hasChildren = cat.subCategories && cat.subCategories.length > 0;
        const isDisabled = disabledValues.includes(cat.catId || 0);

        return {
          label: cat.catName || '',
          value: cat.catId || 0,
          children: hasChildren ? convertToOptions(cat.subCategories!) : undefined,
          // 禁用条件：1. 在禁用列表中（已被其他规则使用）
          // 父节点不禁用，允许点击展开，但在 handleChange 中会被过滤
          disabled: isDisabled,
        };
      });
    };

    const rootCategories = buildRootCategories();
    return convertToOptions(rootCategories);
  }, [categoriesMap, categories, disabledValues]);

  // 处理值变化 - 只有选择叶子节点时才更新值
  const handleChange = (selectedValue: any) => {
    if (multiple) {
      // 对于多选，过滤出叶子节点
      const leafValues = selectedValue.filter((path: number[]) => {
        const leafCatId = path[path.length - 1];
        return isLeafNode(leafCatId);
      });
      onChange?.(leafValues);
    } else {
      // 对于单选，检查是否为叶子节点
      if (Array.isArray(selectedValue) && selectedValue.length > 0) {
        const finalValue = selectedValue[selectedValue.length - 1];
        if (isLeafNode(finalValue)) {
          onChange?.(finalValue);
        }
        // 如果不是叶子节点，不调用 onChange，保持当前值不变
      }
    }
  };

  // 处理单选时的值格式转换
  const processedValue = useMemo(() => {
    if (multiple) {
      return value;
    }

    // 对于单选，需要找到完整的路径
    if (typeof value === 'number' && value > 0) {
      const findPath = (options: CategoryOption[], targetValue: number): number[] | null => {
        for (const option of options) {
          if (option.value === targetValue) {
            return [option.value];
          }
          if (option.children) {
            const childPath = findPath(option.children, targetValue);
            if (childPath) {
              return [option.value, ...childPath];
            }
          }
        }
        return null;
      };

      return findPath(categoryOptions, value) || undefined;
    }

    return undefined;
  }, [value, categoryOptions, multiple]);

  const cascaderProps: CascaderProps<any> = {
    options: categoryOptions,
    value: processedValue,
    onChange: handleChange,
    placeholder: loading ? 'Loading categories...' : placeholder,
    multiple,
    loading,
    disabled: disabled || loading,
    style: { width: '100%', ...customStyle },
    showSearch: true,
    changeOnSelect: true, // 允许选择任何节点，但在 handleChange 中过滤
    expandTrigger: 'click', // 点击展开和选择
  };

  return <Cascader {...cascaderProps} />;
};

export default BasicCategoryCascader;
